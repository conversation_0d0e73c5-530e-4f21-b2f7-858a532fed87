<template>
  <view class="flex flex-col items-center justify-center ios-safe-container" style="height: 100vh;">
    <view class="fixed top-0 left-0 w-full h-10 header flex items-center p-2 bg-[#FEFCF8]" style="z-index: 10;"
      v-if="false">
      <up-icon class="absolute left-0" size="24" color="#666" name="arrow-left" @click="goBack"></up-icon>
      <text class="text-center flex-1 text-lg font-medium">美学设计</text>
    </view>

    <up-navbar title="美学设计" @leftClick="goBack">
    </up-navbar>
<!-- 美学诊断 -->
    <aestheticDiagnosis ref="aestheticDiagnosisRef" :reports="reports" :icons="icons" :buttonIndex="buttonIndex" v-if="buttonIndex == 0"
      :compareFlag="compareFlag" :peopleImg='peopleImg' v-model:activeReport="activeReport"
      @update:loading="updateLoading" @update:percent="updatePercent" @show-login="showLoginModal = true" />

      <!-- 皮肤分析 -->
    <skinAnalysis ref="skinAnalysisRef" v-if="buttonIndex==2" :peopleImg='peopleImg' @update:loading="updateLoading" @update:percent="updatePercent" @show-login="showLoginModal = true"></skinAnalysis>

      <!-- 3D仿真 -->
      <model v-if="buttonIndex == 3" @update:loading="updateLoading" @update:percent="updatePercent"></model>


    <view style="transition: all 0.3s;justify-content: center;" class="w-full flex-1 flex flex-col items-center" v-if="buttonIndex == -1">

      <image ref="beforeimgRef" :src="peopleImg" mode="widthFix" style="width: 100%;" />
      <!-- <ImgCompare v-else :before="peopleImg" :after="peopleImg" :height="beforeImgHeight" /> -->


    </view>
  </view>
  <!-- 底部导航栏1 -->
  <view v-show="buttonIndex==-1"
    class="fixed bottom-0 left-0 w-full bg-white flex flex-row justify-between items-center px-6 py-4 border-t border-[#f0f0f0] iconGround transition-transform duration-300 transform ios-bottom-safe"
    :class="{ 'translate-y-0': !activeReport, 'translate-y-full': activeReport }">
    <!-- 美学诊断 -->
    <view v-for="(item, index) in icons" class="flex flex-col items-center flex-1" @click="selectIcon(index)">
      <img :src="item.selected ? item.activeUrl : item.url" alt="">
      <text class="text-[#6d6d6d] text-xs mt-1">{{ item.label }}</text>
    </view>
  </view>



  <!-- Loading Overlay -->
  <view v-if="loading && buttonIndex != -1" class="loading-overlay ios-loading-overlay">
    <view class="loading-content">
      <text class="loading-text" v-if="s == '正在生成中'">分析中,{{ s }}...</text>
      <text class="loading-text" v-else>分析中,预计还有{{ s }}秒...</text>
      <progress class="loading-progress" activeColor="#1890ff" backgroundColor="#f0f0f0" :percent="percent" />
    </view>
  </view>

  <!-- 登录弹窗 -->
  <up-popup :show="showLoginModal" mode="center" :round="20" :closeable="true" @close="closeLoginModal">
    <view class="login-modal">
      <view class="login-header">
        <text class="login-title">手机号登录</text>
        <text class="login-subtitle">请输入手机号和验证码进行登录</text>
      </view>

      <view class="login-form">
        <!-- 手机号输入 -->
        <view class="form-item">
          <up-input v-model="phoneNumber" placeholder="请输入手机号" type="number" maxlength="11" :border="true"
            :clearable="true">
            <template #prefix>
              <up-icon name="phone" size="20" color="#999"></up-icon>
            </template>
          </up-input>
        </view>

        <!-- 验证码输入 -->
        <view class="form-item">
          <up-input v-model="verificationCode" placeholder="请输入验证码" type="number" maxlength="6" :border="true"
            :clearable="true">
            <template #prefix>
              <up-icon name="lock" size="20" color="#999"></up-icon>
            </template>
            <template #suffix>
              <up-button :text="isCodeSent ? `${countdown}s` : '获取验证码'" :disabled="isCodeSent || isLoading"
                :loading="isLoading" size="mini" type="primary" color="rgb(255 143 156)" @click="sendVerificationCode"
                style="width: 120rpx; height: 60rpx;"></up-button>
            </template>
          </up-input>
        </view>
      </view>

      <view class="login-actions">
        <up-button text="登录" type="primary" :loading="isLoading" color="rgb(255 143 156)" @click="handleLogin"
          style="width: 100%; height: 80rpx; margin-top: 40rpx;"></up-button>
      </view>
    </view>
  </up-popup>

</template>

<script setup>
import model from "/components/3D/3D"
import aestheticDiagnosis from "@/components/aestheticDiagnosis/aestheticDiagnosis.vue";
import skinAnalysis  from "@/components/skinAnalysis/skinAnalysis.vue"
import { ref, computed, watch } from 'vue';
import { sendSmsCode, loginWithSms } from "@/Api/index.js"


import {
  onLoad
} from '@dcloudio/uni-app';


const peopleImg = ref('/static/imgs/people.png')
const beforeimgRef = ref(null)
const aestheticDiagnosisRef = ref(null)
const skinAnalysisRef = ref(null)

onLoad((options) => {
  if (options.img) {
    peopleImg.value = options.img
  }
})


let s = ref('60')
const timer = setInterval(() => {
  if(s.value == '正在生成中'){
    return
  }
  let sc = parseInt(s.value);
  sc = sc - 1
  if(sc <= 0) {
    s.value = '正在生成中'
    clearInterval(timer) // 清空计时器
  } else {
    s.value = sc.toString();
  }
}, 1000);
const reports = ['诊断报告', '美学方案', '专属推荐'];
// 定义图标状态
const icons = ref([
  { label: '美学诊断', url: '/static/icons/skin_icon1.png', activeUrl: '/static/icons/skin_icon1_active.png', selected: true, reports },
  { label: '面部分析', url: '/static/icons/skin_icon2.png', activeUrl: '/static/icons/skin_icon2_active.png', selected: false },
  { label: '皮肤分析', url: '/static/icons/skin_icon3.png', activeUrl: '/static/icons/skin_icon3_active.png', selected: false },
  { label: '3D仿真', url: '/static/icons/skin_icon4.png', activeUrl: '/static/icons/skin_icon4_active.png', selected: false }
])






const activeReport = ref('')// 选择图标方法
let buttonIndex = ref(0)
let percent = ref(0);
const loading = ref(false)

// 登录弹窗相关状态
const showLoginModal = ref(false)
const phoneNumber = ref('')
const verificationCode = ref('')
const countdown = ref(0)
const isCodeSent = ref(false)
const isLoading = ref(false)
const pendingAction = ref('') // 记录待执行的动作
const selectIcon = (index) => {
  // 如果是皮肤分析，需要检查登录状态
  if(index == 2){
    // 检查用户是否已登录
    const userInfo = uni.getStorageSync('userInfo')

    if (!userInfo) {
      // 未登录，记录待执行动作并显示登录弹窗
      pendingAction.value = 'skinAnalysis'
      showLoginModal.value = true
      return
    }
  }

  // 已登录或不是皮肤分析，执行切换逻辑
  buttonIndex.value = index
  // 重置所有图标状态
  icons.value.forEach((icon, i) => {
    icon.selected = i === index
  })
}

// 更新loading状态
const updateLoading = (value) => {
  console.log("updateLoading：",value);
  
  loading.value = value
  if(!value){
    clearInterval(timer)
  }
}

// 更新进度
const updatePercent = (value) => {
  console.log("updatePercent：",value);
  percent.value = value
}



// 关闭弹窗
const closePopup = () => {
  // popupVisible.value = false;
  setTimeout(() => {
    activeReport.value = '';
    buttonIndex.value = -1
    icons.value.forEach((icon) => {
      icon.selected = false;
    })
  }, 300);
}


// 返回上一页
const goBack = () => {
  if (buttonIndex.value!=-1) {
    closePopup()
  } else {
    uni.navigateBack()
  }

};


const compareFlag = computed(() => {
  return previewFlag.value && activeReport.value == '美学方案'
})

const previewFlag = ref(false)
watch(() => activeReport.value, (newVal) => {
  console.log('父组件:', newVal);
});

// 发送验证码
const sendVerificationCode = async () => {
  if (!phoneNumber.value) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none'
    })
    return
  }

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(phoneNumber.value)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    })
    return
  }

  try {
    isLoading.value = true
    // 调用发送验证码的API
    const response = await sendSmsCode({ phone: phoneNumber.value })

    if (response.data.code === 200) {
      isCodeSent.value = true
      countdown.value = 60

      // 开始倒计时
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
          isCodeSent.value = false
        }
      }, 1000)

      uni.showToast({
        title: '验证码已发送',
        icon: 'none'
      })
    } else {
      uni.showToast({
        title: response.data.msg || '发送失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
  }
}

// 登录
const handleLogin = async () => {
  if (!phoneNumber.value) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none'
    })
    return
  }

  if (!verificationCode.value) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none'
    })
    return
  }

  try {
    isLoading.value = true
    // 调用登录API
    const response = await loginWithSms({
      phone: phoneNumber.value,
      captcha: verificationCode.value,
      dept: "1",
      operationId: uni.getStorageSync('operationId')
    })

    if (response.data.code === 200) {
      // 登录成功，保存token和用户信息
      uni.setStorageSync('userInfo', response.data.data)

      // 关闭弹窗
      showLoginModal.value = false

      // 重置表单
      phoneNumber.value = ''
      verificationCode.value = ''
      isCodeSent.value = false
      countdown.value = 0

      uni.showToast({
        title: '登录成功',
        icon: 'none'
      })

      // 登录成功后继续执行原有逻辑
      setTimeout(() => {
        // 继续执行皮肤分析或其他功能
        continueAfterLogin()
      }, 500)
    } else {
      uni.showToast({
        title: response.data.msg || '登录失败',
        icon: 'none'
      })
    }

  } catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
  }
}

// 关闭登录弹窗
const closeLoginModal = () => {
  showLoginModal.value = false
  phoneNumber.value = ''
  verificationCode.value = ''
  isCodeSent.value = false
  countdown.value = 0
  pendingAction.value = '' // 重置待执行动作
}

// 登录成功后继续执行的逻辑
const continueAfterLogin = () => {
  // 检查是否需要切换到皮肤分析页面
  // 这里我们需要一个变量来记录用户点击的是哪个功能
  if (pendingAction.value === 'skinAnalysis') {
    // 切换到皮肤分析页面
    buttonIndex.value = 2
    icons.value.forEach((icon, i) => {
      icon.selected = i === 2
    })
    // 重置待执行动作
    pendingAction.value = ''
  }
  // 如果是美学诊断的预览功能，需要通知子组件继续执行
  else if (buttonIndex.value === 0 && aestheticDiagnosisRef.value) {
    // 调用子组件的expandPreview方法
    aestheticDiagnosisRef.value.expandPreview()
  }
}

</script>

<style scoped lang="scss">


/* Loading Overlay Styles */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9;
}

.loading-content {
  // background-color: white;
  border-radius: 10rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80%;
}

.loading-progress {
  width: 100%;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #fff;
}

/* 全局样式 */
page {
  background-color: #FEFCF8;
  font-family: 'Noto Sans SC', sans-serif;
}

/* iOS适配样式 */
.ios-safe-container {
  padding-top: env(safe-area-inset-top);
  /* iOS状态栏安全区域 */
}

.ios-bottom-safe {
  padding-bottom: env(safe-area-inset-bottom);
  /* iOS底部安全区域 */
}

.ios-loading-overlay {
  /* iOS加载层优化 */
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

/* iOS滚动和触摸优化 */
.iconGround {
  -webkit-overflow-scrolling: touch;

  img {
    width: 50rpx;
    height: 50rpx;
    margin-bottom: 10rpx;
    /* iOS图片优化 */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}

/* iOS按钮点击效果优化 */
view[class*="flex"] {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* iOS进度条优化 */
.loading-progress {
  width: 100%;
  margin-bottom: 20rpx;
  /* iOS进度条样式优化 */
  -webkit-appearance: none;
  appearance: none;
}

/* iOS文本选择优化 */
text {
  -webkit-user-select: none;
  user-select: none;
}

/* 登录弹窗样式 */
.login-modal {
  width: 600rpx;
  padding: 60rpx 40rpx 40rpx;
  background: #fff;
  border-radius: 20rpx;
}

.login-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.login-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.login-subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.login-form {
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.login-actions {
  margin-top: 40rpx;
}
</style>