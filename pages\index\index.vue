<template>
    <view class="min-h-screen bg-white pb-2">
        <!-- 顶部区域 -->
        <view class="relative containerBack">


            <!-- 标题区域 -->
            <view class="pt-4 pb-2 px-6">
                <view class="flex items-center text-center" style="display: flex;
                    flex-direction: row;
                    flex-wrap: wrap;
                    align-content: stretch;
                    justify-content: flex-end;">
                    <view>
                        <text class="text-lg font-montserrat text-gray-500 leading-tight" style="font-size: 22rpx;">
                            Beauty is natural, youth is a blessing
                        </text>
                        <text class="block text-lg font-montserrat text-gray-500 leading-tight"
                            style="font-size: 22rpx;">
                            Genuine care. nature beauty.
                        </text>

                    </view>
                    <!-- 姐妹众测标志 -->
                    <view class="text-center">
                        <view class="w-8 h-8 rounded-full flex items-center justify-center" style="margin: 0 auto;">
                            <view class="relative">
                                <view class="absolute w-2 h-2 bg-orange-400 rounded-full right-0 top-0"></view>
                                <view class="w-6 h-6 rounded-full bg-[#4ecdc4] flex items-center justify-center">
                                    <view class="w-3 h-3 rounded-full bg-[#f8d6db]"></view>
                                </view>
                            </view>
                        </view>
                        <view class="text-gray-700 text-xs font-noto">姐妹众测</view>
                    </view>
                </view>

                <view class="border-t border-pink-300 mt-2 mb-2 mr-0 w-3/4 mx-auto"></view>
                <view class="flex items-center mt-2">
                    <view>
                        <text class="text-5xl font-noto text-black leading-none" style="font-size: 80rpx;">美学设计</text>
                    </view>
                    <view class="ml-2 flex flex-col justify-center">
                        <text class="font-montserrat tracking-wide" style="font-size: 22rpx;">FORETELL
                            AI</text>
                    </view>
                </view>

                <view class="mt-2">
                    <button class="bg-[#E65D5D] text-white font-noto ml-0"
                        style="font-size: 22rpx;line-height: 40rpx; width: 200rpx;height: 40rpx;border-radius: 0;">
                        免费检测
                    </button>
                </view>
            </view>
            <!-- 底部引用 -->
            <view class="flex items-center px-4 pb-20 absolute bottom-0 left-0">
                <view class="flex-1">
                    <text class="text-gray-400 font-noto text-2xl" style="font-size: 26rpx;">「美，本该理性而自信」</text>
                </view>
            </view>
        </view>

        <!-- 点阵分隔条 -->
        <view class="w-full bg-[#8fc3d6] py-4 flex justify-center items-center">
            <view v-for="i in 14" :key="i" class="dot"></view>
        </view>

        <!-- 相机和上传按钮 -->
        <view class="w-full flex flex-col items-center mt-8 mb-8">
            <!-- 相机按钮 -->
            <!-- <view @click="handlepicpic"
                class="w-11/12 max-w-xl bg-[#fff3f3] rounded-2xl shadow-custom mb-6 pt-16 px-4 border-b-4 button-camora">
                <view class="flex flex-col items-center">
                    <text class="font-noto text-2xl text-[#999696]" style="font-size: 26rpx;">相机拍摄</text>
                </view>
            </view> -->

            <!-- 上传按钮 -->
            <view class="w-11/12 max-w-xl bg-[#fff7ee] rounded-2xl shadow-custom  pt-16 px-4 border-b-4 button-upload"
                @click="selectAndUploadFile">
                <view class="flex flex-col items-center">
                    <text class="font-noto text-2xl text-[#999696]" style="font-size: 26rpx;">导入照片</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>

import { uploadToOSS, welcome, callAi, invokeDetectData } from '@/Api/index';
import { onMounted } from 'vue';

// Define emit for composition API
const emit = defineEmits(['upload']);



const handlepicpic = () => {
    uni.navigateTo({
        url: '/pages/picpic/index'
    })
}

// 新增：选择并上传文件
const selectAndUploadFile = () => {
    uni.chooseImage({
        count: 1,
        success: (res) => {
            const tempFile = res.tempFiles[0];
            handleFileUpload(tempFile);
        },
        fail: (err) => {
            console.error('选择图片失败:', err);
        }
    });
};

// 处理文件选择
const handleFileUpload = async (file) => {
    // uni.navigateTo({ url: '/pages/skinAnalysis/start?img=' });
    // return
    try {
        // 显示上传进度
        const response = await uploadToOSS(file, async () => {
        });
        if (response.data.code == 200) {
            let { data:res } = await invokeDetectData({
                "imageUrl": response.data.data.url,
                "deptId": "1"
            })
            if(res.code!=200){
                return uni.showToast({
                    title:"未检测出人脸",
                    icon:"none"
                })
            }
            
            let { data } = await callAi({ imageUrl: response.data.data.url, deptId: '1' ,operationId:res.data.operationId})
            uni.setStorage({
                key: 'operationId',
                data: res.data.operationId,
                success: function () {
                    uni.navigateTo({ url: '/pages/skinAnalysis/start?img=' + response.data.data.url });
                }
            });


        } else {
            uni.showToast({
                title: response.data.msg,
                icon: 'none'
            });
        }
    } catch (error) {
        console.error('上传失败:', error);
        // 使用 uni.showToast 替代 alert
        uni.showToast({
            title: '上传失败',
            icon: 'none'
        });
    }
};


async function getWelcome() {
    let { data } = await welcome()
    console.log(data.data);
    uni.setStorageSync('welcome', data.data)

}
onMounted(() => {

    getWelcome()
})
</script>

<style lang="scss">
.containerBack {
    height: 1000rpx;
    background: url("/static/imgs/banner.png");
    background-size: cover;
}

.button-camora {
    background: url("/static/imgs/camora.png");
    background-size: cover;
}

.button-upload {
    background: url("/static/imgs/upload.png");
    background-size: cover;
}

/* 全局样式 */
page {
    background-color: #fff;
    font-family: 'Noto Sans SC', 'Montserrat', Arial, sans-serif;
}

.font-montserrat {
    font-family: 'Montserrat', Arial, sans-serif;
}

.font-noto {
    font-family: 'Noto Sans SC', Arial, sans-serif;
}

.tracking-wide {
    letter-spacing: 0.04em;
}

.rounded-2xl {
    border-radius: 16rpx;
}

.rounded-3xl {
    border-radius: 24rpx;
}

.shadow-custom {
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.dot {
    width: 32rpx;
    height: 32rpx;
    background: #fff;
    border-radius: 50%;
    display: inline-block;
    margin: 0 8rpx;
    border: 2rpx solid #b7dbe6;
}

.status-bar {
    font-family: 'Montserrat', Arial, sans-serif;
    font-size: 22rpx;
    font-weight: 700;
    letter-spacing: 0.02em;
}

.notch {
    width: 140rpx;
    height: 8rpx;
    background: #000;
    border-radius: 4rpx;
    margin: 24rpx auto 8rpx;
}

/* 响应式设计 */
@media (max-width: 500rpx) {
    .main-title {
        font-size: 36rpx;
    }

    .main-title-en {
        font-size: 18rpx;
    }

    .quote {
        font-size: 18rpx;
    }

    .camera-btn,
    .upload-btn {
        padding: 20rpx 0 16rpx 0;
    }
}
</style>