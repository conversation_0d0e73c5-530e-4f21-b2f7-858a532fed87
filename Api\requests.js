import { $http } from '@escook/request-miniprogram'

// uni.$http = $http
// 配置请求根路径
// $http.baseUrl = '/api'

// 请求开始之前做一些事情
$http.beforeRequest = function (options) {
	if(options.url.startsWith('/oss')){
		options.header = {
			"Authorization": uni.getStorageSync("welcome")||'',
		}
	}
  options.header = {
  	"Authorization": uni.getStorageSync("welcome")||'',
  }
}

// 请求完成之后做一些事情
$http.afterRequest = function (res) {
  if (res.data.code != 200) {
    // 抛出错误，让 Promise 进入 reject
    // throw new Error(res.data.msg || '请求失败');
  }
}

export default $http